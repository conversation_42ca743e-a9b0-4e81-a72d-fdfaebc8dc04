# Simple IoT Vue3 迁移项目上下文文档

## 🔄 快速恢复上下文指南

> 本文档用于AI助手快速了解项目当前状态，避免重复分析。请在每次新会话开始时首先阅读此文档。

## 📋 项目概述

**项目名称**: Simple IoT 前端 Vue3 迁移  
**项目状态**: 规划阶段  
**开始日期**: 2024-12-28  
**预计完成**: 2025-04-22 (16周)  
**当前阶段**: 方案设计完成，等待实施

## 🎯 核心目标

将Simple IoT项目的前端从**Elm语言**迁移到**Vue3 + TypeScript**，采用**增量迁移**策略，确保业务连续性和风险可控。

## 🏗️ 技术栈决策

### 当前系统 (待迁移)
- **语言**: Elm 0.19.1 (函数式编程)
- **框架**: elm-spa 1.0.0 
- **UI库**: elm-ui 1.1.8
- **项目规模**: 30+个设备类型，主页面1916行代码

### 目标系统 (新架构)
- **语言**: TypeScript 5.0+ (严格模式)
- **框架**: Vue 3.3+ (Composition API)
- **构建工具**: Vite 4.0+
- **UI库**: **Ant Design Vue 4.0+** (已确定)
- **状态管理**: Pinia 2.0+
- **路由**: Vue Router 4.0+
- **HTTP客户端**: Axios + @tanstack/vue-query
- **样式**: Less + CSS Modules
- **测试**: Vitest + Vue Test Utils

## 📊 项目规模分析

### 核心文件复杂度
- **Pages/Home_.elm**: 1916行 (超复杂主页面)
- **Api/Point.elm**: 1389行 (复杂数据结构)
- **UI/NodeInputs.elm**: 885行 (复杂输入组件)
- **Api/Node.elm**: 461行 (核心API)
- **30+个设备组件**: 平均100-300行

### 设备类型列表
```
高频设备 (优先级1): NodeDevice, NodeModbus, NodeVariable, NodeGroup, NodeAction
中频设备 (优先级2): NodeModbusIO, NodeOneWire, NodeSerial, NodeCondition, NodeRule, NodeShelly, NodeShellyIO, NodeParticle, NodeSync, NodeNTP
低频设备 (优先级3): NodeCanBus, NodeSignalGenerator, NodeNetworkManager, NodeFile, NodeMetrics, NodeDb, NodeUpdate, NodeUser, 其他专用设备
```

## 🔀 迁移策略

### 增量迁移方案
采用**并行运行**架构，通过前端代理(Nginx)实现路由分发：
- `/v1/*` -> Elm System (现有系统)
- `/v2/*` -> Vue3 System (新系统)
- `/api/*` -> Go Backend (共享API)

### 四个阶段计划
1. **第一阶段** (3周): 基础架构 + 认证模块
2. **第二阶段** (4周): 核心设备管理 + 前5个设备类型
3. **第三阶段** (5周): 中频设备类型 + 数据点管理
4. **第四阶段** (4周): 剩余设备类型 + 性能优化

## 📁 新项目结构

```
frontend-vue3/
├── src/
│   ├── api/              # API接口层
│   ├── components/       # 组件库
│   │   ├── common/       # 基础组件
│   │   ├── node/         # 30+个设备组件
│   │   └── ui/           # UI组件
│   ├── composables/      # 组合式API
│   ├── pages/            # 页面组件
│   ├── router/           # 路由配置
│   ├── stores/           # Pinia状态管理
│   ├── types/            # TypeScript类型
│   ├── utils/            # 工具函数
│   └── styles/           # 样式文件
├── tests/                # 测试文件
├── vite.config.ts        # Vite配置
└── package.json          # 依赖配置
```

## 🔧 关键技术决策

### 1. UI框架选择
- **决策**: Ant Design Vue 4.0+
- **理由**: 企业级组件库，适合IoT管理界面，生态完善
- **关键组件**: Card, Button, Form, Table, Tree, Modal等

### 2. 状态管理
- **决策**: Pinia 2.0+
- **理由**: Vue3官方推荐，API简洁，TypeScript支持良好
- **架构**: 模块化Store设计

### 3. 数据类型迁移
- **Elm类型** -> **TypeScript接口**
- **运行时验证**: 使用zod库
- **API兼容性**: 保持与Go后端的兼容

### 4. 组件迁移模式
```vue
<!-- 标准组件模式 -->
<template>
  <a-card>
    <template #title>{{ nodeTitle }}</template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="handleEdit">编辑</a-button>
        <a-button type="text" @click="handleDelete">删除</a-button>
      </a-space>
    </template>
    <div class="node-content">
      <slot />
    </div>
  </a-card>
</template>
```

## 🧪 测试策略

### 每阶段测试要求
- **功能测试**: 与原系统功能完全一致
- **兼容性测试**: API兼容，数据格式兼容
- **性能测试**: 页面响应时间<2s，内存使用合理
- **安全测试**: 认证安全，XSS/CSRF防护

### 测试工具栈
- **单元测试**: Vitest + Vue Test Utils
- **集成测试**: API测试覆盖
- **E2E测试**: Playwright
- **性能测试**: Web Vitals监控

## 📈 项目进度跟踪

### 进度文档
- **详细进度**: `docs/replace/progress.xml`
- **技术方案**: `docs/replace/task.md`
- **快速概览**: `docs/replace/README.md`

### 当前状态
- **阶段**: 规划完成
- **下一步**: 开始第一阶段实施
- **关键里程碑**: 2025-01-18 第一阶段验收

## 🚨 关键风险点

### 高风险项
1. **业务逻辑复杂**: 30+设备类型，每种都有特定逻辑
2. **数据结构复杂**: Point.elm有1389行复杂数据处理
3. **主页面复杂**: Home_.elm有1916行业务逻辑

### 风险缓解
- 详细的功能对照表
- 分阶段测试验证
- 保留原系统作为回滚方案

## 🎛️ 开发环境配置

### 必要工具
- **Node.js**: 18.0+
- **包管理**: pnpm (推荐)
- **IDE**: VS Code + Vue3插件
- **代码质量**: ESLint + Prettier + husky

### 关键依赖
```json
{
  "vue": "^3.3.0",
  "vue-router": "^4.0.0",
  "pinia": "^2.0.0",
  "ant-design-vue": "^4.0.0",
  "@ant-design/icons-vue": "^7.0.0",
  "axios": "^1.6.0",
  "@tanstack/vue-query": "^5.0.0",
  "typescript": "^5.0.0",
  "vite": "^4.0.0",
  "vitest": "^1.0.0"
}
```

## 💡 重要提醒

### 对AI助手的指导
1. **UI库固定**: 必须使用Ant Design Vue，不要建议其他UI库
2. **迁移策略**: 采用增量迁移，不要建议一次性重写
3. **测试要求**: 每个阶段都需要完整的测试验证
4. **风险控制**: 始终考虑回滚方案和业务连续性

### 常见问题
- **Q**: 为什么不用Element Plus？
- **A**: 项目需求已确定使用Ant Design Vue，请勿更改

- **Q**: 可以跳过某个测试阶段吗？
- **A**: 不可以，每个阶段的测试都是必需的

- **Q**: 可以同时迁移多个设备类型吗？
- **A**: 可以，但需要确保测试覆盖和质量控制

## 📞 联系信息

**项目负责人**: 开发团队  
**技术文档**: `docs/replace/` 目录  
**进度跟踪**: `progress.xml`  
**技术决策**: `task.md`

---

**最后更新**: 2024-12-28  
**文档版本**: 1.0  
**状态**: 规划阶段完成

> 💡 **提示**: 开始新的开发工作前，请先查看 `progress.xml` 了解当前具体进度，然后参考 `task.md` 获取详细技术实现指导。 