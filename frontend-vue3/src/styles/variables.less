// Ant Design Vue 主题变量
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 字体
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;

// 间距
@padding-xs: 8px;
@padding-sm: 12px;
@padding-md: 16px;
@padding-lg: 24px;
@padding-xl: 32px;

// 边框
@border-radius-base: 6px;
@border-color-base: #d9d9d9;

// 阴影
@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

// 布局
@layout-header-height: 64px;
@layout-sider-width: 256px;

// 自定义颜色
@node-device-color: #1890ff;
@node-modbus-color: #52c41a;
@node-variable-color: #faad14;
@node-group-color: #722ed1;
@node-action-color: #f5222d;
