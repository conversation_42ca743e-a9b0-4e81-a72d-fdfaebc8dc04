// 路由配置
import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { StorageUtil } from '@/utils/storage'

// 路由定义
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Home',
        redirect: '/monitoring/dashboard'
      },
      // 设备管理
      {
        path: '/devices',
        name: 'Devices',
        redirect: '/devices/tree'
      },
      {
        path: '/devices/tree',
        name: 'DeviceTree',
        component: () => import('@/pages/devices/DeviceTree.vue'),
        meta: {
          title: '设备管理'
        }
      },
      {
        path: '/devices/groups',
        name: 'DeviceGroups',
        component: () => import('@/pages/devices/DeviceGroups.vue'),
        meta: {
          title: '设备分组'
        }
      },
      // 监控中心
      {
        path: '/monitoring',
        name: 'Monitoring',
        redirect: '/monitoring/dashboard'
      },
      {
        path: '/monitoring/dashboard',
        name: 'Dashboard',
        component: () => import('@/pages/monitoring/Dashboard.vue'),
        meta: {
          title: '监控面板'
        }
      },
      {
        path: '/monitoring/real-time',
        name: 'RealTimeData',
        component: () => import('@/pages/monitoring/RealTimeData.vue'),
        meta: {
          title: '实时数据'
        }
      },
      {
        path: '/monitoring/history',
        name: 'HistoryData',
        component: () => import('@/pages/monitoring/HistoryData.vue'),
        meta: {
          title: '历史数据'
        }
      },
      {
        path: '/monitoring/alerts',
        name: 'AlertManagement',
        component: () => import('@/pages/monitoring/AlertManagement.vue'),
        meta: {
          title: '告警管理'
        }
      },
      // 规则引擎
      {
        path: '/rules',
        name: 'Rules',
        redirect: '/rules/list'
      },
      {
        path: '/rules/list',
        name: 'RuleList',
        component: () => import('@/pages/rules/RuleList.vue'),
        meta: {
          title: '规则列表'
        }
      },
      {
        path: '/rules/conditions',
        name: 'ConditionConfig',
        component: () => import('@/pages/rules/ConditionConfig.vue'),
        meta: {
          title: '条件配置'
        }
      },
      {
        path: '/rules/actions',
        name: 'ActionConfig',
        component: () => import('@/pages/rules/ActionConfig.vue'),
        meta: {
          title: '动作配置'
        }
      },
      // 网络配置
      {
        path: '/network',
        name: 'Network',
        redirect: '/network/modbus'
      },
      {
        path: '/network/modbus',
        name: 'ModbusConfig',
        component: () => import('@/pages/network/ModbusConfig.vue'),
        meta: {
          title: 'Modbus配置'
        }
      },
      {
        path: '/network/serial',
        name: 'SerialConfig',
        component: () => import('@/pages/network/SerialConfig.vue'),
        meta: {
          title: '串口配置'
        }
      },
      {
        path: '/network/canbus',
        name: 'CanBusConfig',
        component: () => import('@/pages/network/CanBusConfig.vue'),
        meta: {
          title: 'CAN总线配置'
        }
      },
      {
        path: '/network/manager',
        name: 'NetworkManager',
        component: () => import('@/pages/network/NetworkManager.vue'),
        meta: {
          title: '网络管理'
        }
      },
      // 系统管理
      {
        path: '/system',
        name: 'System',
        redirect: '/system/users'
      },
      {
        path: '/system/users',
        name: 'UserManagement',
        component: () => import('@/pages/system/UserManagement.vue'),
        meta: {
          title: '用户管理'
        }
      },
      {
        path: '/system/logs',
        name: 'SystemLogs',
        component: () => import('@/pages/system/SystemLogs.vue'),
        meta: {
          title: '系统日志'
        }
      },
      {
        path: '/system/backup',
        name: 'BackupRestore',
        component: () => import('@/pages/system/BackupRestore.vue'),
        meta: {
          title: '备份恢复'
        }
      },
      {
        path: '/system/update',
        name: 'SystemUpdate',
        component: () => import('@/pages/system/SystemUpdate.vue'),
        meta: {
          title: '系统更新'
        }
      }
    ]
  },
  {
    path: '/login',
    name: 'SignIn',
    component: () => import('@/pages/SignIn.vue'),
    meta: {
      requiresAuth: false,
      title: '登录'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/pages/NotFound.vue'),
    meta: {
      requiresAuth: false,
      title: '页面未找到'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(_to, _from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Simple IoT`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const authStore = useAuthStore()
    
    // 如果没有认证状态，尝试从本地存储恢复
    if (!authStore.isAuthenticated) {
      const token = StorageUtil.getAuthToken()
      const userInfo = StorageUtil.getUserInfo()
      
      if (token && userInfo) {
        // 恢复认证状态
        authStore.initAuth()
        
        // 验证 token 有效性
        const isValid = await authStore.validateToken()
        if (!isValid) {
          next('/login')
          return
        }
      } else {
        next('/login')
        return
      }
    }
  }

  // 如果已经登录，访问登录页面时重定向到首页
  if (to.name === 'SignIn') {
    const authStore = useAuthStore()
    if (authStore.isAuthenticated) {
      next('/')
      return
    }
  }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等逻辑
  console.log(`Navigation: ${from.path} -> ${to.path}`)
})

export default router
