// 节点/设备 API
import { HttpClient } from '@/utils/http'
import type {
  Node,
  NodeEdit,
  CreateNodeRequest,
  CreateNodeSendRequest,
  UpdateNodeRequest,
  UpdateNodeSendRequest,
  NodeSchemaType,
  CreateNodeRequestType
} from '@/types/node'
import { NodeSchema, CreateNodeRequestSchema } from '@/types/node'
import type { ApiResponse, RequestParams } from '@/types/common'

export class NodeAPI {
  /**
   * 获取所有节点列表
   */
  static async getNodes(params?: RequestParams): Promise<Node[]> {
    const response = await HttpClient.get<Node[]>('/v1/nodes', { params })
    
    // 验证响应数据
    const nodes = response.map(node => NodeSchema.parse(node))
    return nodes
  }

  /**
   * 根据ID获取单个节点 - 从本地节点列表中查找
   * 注意：不直接调用后端API，而是从已获取的节点列表中查找
   */
  static getNodeFromList(nodes: Node[], id: string): Node | undefined {
    return nodes.find(node => node.id === id)
  }

  /**
   * 创建新节点
   */
  static async createNode(data: CreateNodeSendRequest): Promise<{ success: boolean; id: string }> {
    // 验证请求数据
    const validatedData = CreateNodeRequestSchema.parse(data)

    const response = await HttpClient.post<{ success: boolean; id: string }>('/v1/nodes', validatedData)

    return response
  }

  /**
   * 更新节点 - 后端通过POST points来更新
   */
  static async updateNode(id: string, data: UpdateNodeSendRequest): Promise<void> {
    // 后端通过POST points来更新节点
    if (data.points && data.points.length > 0) {
      await HttpClient.post(`/v1/nodes/${id}/points`, data.points)
    }

    // 如果需要移动节点到新父节点（只有当父节点真正改变时才调用）
    if (data.parent !== undefined && data.oldParent !== undefined && data.parent !== data.oldParent) {
      await HttpClient.post(`/v1/nodes/${id}/parents`, {
        ID: id,
        OldParent: data.oldParent,
        NewParent: data.parent
      })
    }

    // 注意：不返回节点数据，调用方应该重新获取所有节点列表来更新状态
    // 这与原有Elm前端的行为一致
  }

  /**
   * 删除节点 - 后端需要parent参数
   */
  static async deleteNode(id: string, parent?: string): Promise<void> {
    const deleteData = parent ? { parent } : {}
    await HttpClient.delete(`/v1/nodes/${id}`, { data: deleteData })
  }

  /**
   * 获取节点的子节点 - 通过获取所有节点然后过滤
   */
  static async getChildNodes(parentId: string): Promise<Node[]> {
    const allNodes = await this.getNodes()
    return allNodes.filter(node => node.parent === parentId)
  }

  /**
   * 移动节点到新的父节点
   */
  static async moveNode(nodeId: string, oldParentId: string, newParentId: string): Promise<void> {
    await HttpClient.post(`/v1/nodes/${nodeId}/parents`, {
      id: nodeId,
      oldParent: oldParentId,
      newParent: newParentId
    })
  }

  /**
   * 复制节点
   */
  static async copyNode(nodeId: string, targetParentId: string): Promise<void> {
    await HttpClient.put(`/v1/nodes/${nodeId}/parents`, {
      id: nodeId,
      newParent: targetParentId,
      duplicate: true
    })
  }

  /**
   * 获取节点树形结构 - 通过获取所有节点然后构建树
   */
  static async getNodeTree(rootId?: string): Promise<Node[]> {
    const allNodes = await this.getNodes()

    // 验证响应数据
    const nodes = allNodes.map(node => NodeSchema.parse(node))
    return nodes
  }

  /**
   * 搜索节点 - 通过获取所有节点然后过滤
   */
  static async searchNodes(query: string, filters?: Record<string, any>): Promise<Node[]> {
    const allNodes = await this.getNodes()

    // 简单的客户端搜索
    let filteredNodes = allNodes.filter(node => {
      const description = this.getNodeDescription(node)
      return node.id.toLowerCase().includes(query.toLowerCase()) ||
             description.toLowerCase().includes(query.toLowerCase())
    })

    // 应用过滤器
    if (filters?.type) {
      filteredNodes = filteredNodes.filter(node => node.type === filters.type)
    }

    return filteredNodes
  }

  /**
   * 获取节点描述的辅助方法
   */
  private static getNodeDescription(node: Node): string {
    const descPoint = node.points.find(p => p.type === 'description')
    return descPoint?.text || descPoint?.value?.toString() || node.id
  }
}

// 导出便捷的API函数
export const nodeApi = {
  getNodes: NodeAPI.getNodes,
  getNodeFromList: NodeAPI.getNodeFromList,
  createNode: NodeAPI.createNode,
  updateNode: NodeAPI.updateNode,
  deleteNode: NodeAPI.deleteNode,
  getChildNodes: NodeAPI.getChildNodes,
  moveNode: NodeAPI.moveNode,
  copyNode: NodeAPI.copyNode,
  getNodeTree: NodeAPI.getNodeTree,
  searchNodes: NodeAPI.searchNodes
}
